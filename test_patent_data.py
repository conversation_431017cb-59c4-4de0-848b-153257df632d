#!/usr/bin/env python3
"""
Quick test to check what's in the database for US7504937B2
"""

import sys
import os
import json

# Add the infringement-agent directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'infringement-agent'))

try:
    from tools.database_utils import get_complete_patent_analysis, init_db
    print("✅ Successfully imported database utilities")
except ImportError as e:
    print(f"❌ Failed to import database utilities: {e}")
    sys.exit(1)

def test_specific_patent():
    """Test the specific patent US7504937B2"""
    
    patent_number = 'US7504937B2'
    print(f"\n🔍 Testing patent: {patent_number}")
    
    try:
        # Initialize database
        init_db()
        
        # Get patent data
        patent_data = get_complete_patent_analysis(patent_number)
        
        if patent_data:
            print(f"✅ Patent {patent_number} found in database")
            print(f"   Data keys: {list(patent_data.keys())}")
            
            # Check analysis_data
            analysis_data = patent_data.get('analysis_data')
            print(f"   Analysis data: {type(analysis_data)} - {analysis_data}")
            
            # Check novelty_data
            novelty_data = patent_data.get('novelty_data')
            print(f"   Novelty data: {type(novelty_data)} - {novelty_data}")
            
            # Check infringed_models
            infringed_models = patent_data.get('infringed_models', [])
            print(f"   Infringed models: {len(infringed_models)} items")
            
            for i, model in enumerate(infringed_models):
                print(f"     Model {i+1}:")
                print(f"       Company: {model.get('company', 'Unknown')}")
                print(f"       Model: {model.get('model', model.get('product_name', 'Unknown'))}")
                print(f"       Risk: {model.get('infringement_risk', model.get('risk', 'Unknown'))}")
                if model.get('claim_chart'):
                    print(f"       Claim chart: {type(model.get('claim_chart'))} - {len(str(model.get('claim_chart')))} chars")
            
            # Print full data as JSON for debugging
            print(f"\n📄 Full patent data:")
            print(json.dumps(patent_data, indent=2, default=str))
            
        else:
            print(f"❌ Patent {patent_number} not found in database")
            
    except Exception as e:
        print(f"❌ Error retrieving patent {patent_number}: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Testing specific patent data...")
    test_specific_patent()
    print("\n✅ Test completed")
