<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Patent Infringement Analyzer</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <div class="col-md-3 col-lg-2 sidebar">
        <div class="sidebar-header">
          <div style="text-align: center; margin-bottom: 10px;margin-top: 10px;">
            <img src="logo.png" width="200px">
          </div>
        </div>

      </div>

      <!-- Main Content -->
      <div class="col-md-9 col-lg-10 main-content">
        <div class="content-header">
          <h2>Patent Infringement Analyzer</h2>
          <p>Enter a patent number to analyze potential infringement</p>
        </div>

        <!-- Alert Container -->
        <div id="alert-container"></div>

        <!-- Input Form -->
        <div class="input-container">
          <div class="input-group mb-3">
            <input type="text" id="patent-input" class="form-control dark-text" placeholder="Enter patent number (e.g., US10741843B2)">
            <button id="analyze-btn" class="btn btn-primary">Analyze</button>
          </div>

          <!-- Add button to toggle multi-patent mode -->
          <div class="text-center mt-3">
            <button id="toggle-multi-patent" class="btn btn-outline-light btn-sm">
              <i class="fas fa-layer-group"></i> Toggle Multi-Patent Mode
            </button>
          </div>

          <!-- Multi-patent input (hidden by default) -->
          <div id="multi-patent-container" class="mt-3 d-none">
            <div class="card bg-dark text-light">
              <div class="card-header">
                Multi-Patent Analysis
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label for="patent-list-input" class="form-label">Enter multiple patent numbers (one per line)</label>
                  <textarea id="patent-list-input" class="form-control bg-dark text-light" rows="4"
                    placeholder="US10741843B2&#10;US9876543B2&#10;EP1234567A1"></textarea>
                </div>
                <div class="form-check patent-family-checkbox">
                  <input class="form-check-input" type="checkbox" id="patent-family-check">
                  <label class="form-check-label" for="patent-family-check">
                    These patents belong to the same family (combine reports)
                  </label>
                </div>
                <button id="analyze-multi-btn" class="btn btn-primary mt-3">Analyze All Patents</button>
              </div>
            </div>

            <!-- Patent list with status -->
            <div id="patent-list" class="patent-list mt-3 d-none">
              <h5>Patent Analysis Queue</h5>
              <div id="patent-items-container">
                <!-- Patent items will be added here dynamically -->
              </div>

              <!-- Multi-patent analysis results -->
              <div id="multi-patent-results" class="mt-4 d-none">
                <h5>Multi-Patent Analysis Results</h5>

                <!-- Combined Products Table -->
                <div class="card bg-dark text-light mt-3">
                  <div class="card-header">
                    <h6 class="mb-0">Combined Potentially Infringing Products</h6>
                  </div>
                  <div class="card-body">
                    <div id="combined-products-content" class="content-panel">
                      <!-- Combined products table will be inserted here -->
                    </div>
                  </div>
                </div>

                <!-- Combined Claim Charts Table -->
                <div class="card bg-dark text-light mt-3">
                  <div class="card-header">
                    <h6 class="mb-0">Combined Claim Chart Analysis</h6>
                  </div>
                  <div class="card-body">
                    <div id="combined-claim-charts-content" class="content-panel">
                      <!-- Combined claim charts table will be inserted here -->
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-3">
                <button id="download-all-btn" class="btn btn-success" disabled>Download All Reports</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Add chat box after the input container -->
        <div class="chat-container mt-4">
          <h5>Custom Analysis Instructions</h5>
          <div class="mb-3">
            <label for="custom-prompt" class="form-label">Add custom instructions for the analysis (optional)</label>
            <textarea id="custom-prompt" class="form-control chat-input" rows="3"
              placeholder="E.g., Focus on automotive industry competitors or Check for SEP status"></textarea>
          </div>
        </div>

        <!-- Interactive Chat/Confirmation Dialog -->
        <div id="confirmation-dialog" class="confirmation-dialog d-none">
          <div class="card bg-dark text-light border-warning">
            <div class="card-header bg-warning text-dark">
              <h6 class="mb-0">Existing Analysis Found</h6>
            </div>
            <div class="card-body">
              <div id="confirmation-message" class="mb-3">
                <!-- Confirmation message will be inserted here -->
              </div>
              <div class="d-flex gap-2">
                <button id="confirm-yes" class="btn btn-success">Yes, Proceed with New Analysis</button>
                <button id="confirm-no" class="btn btn-primary">No, Use Existing Analysis</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loading-indicator" class="d-none text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <div class="progress mt-3" style="height: 10px;">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
          </div>
          <!-- Loading text will be added here by JavaScript -->
        </div>

        <!-- Results Container -->
        <div id="results-container" class="d-none">
          <ul class="nav nav-tabs" id="resultTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="summary-tab" data-bs-toggle="tab" data-bs-target="#summary" type="button" role="tab" aria-controls="summary" aria-selected="true">Summary</button>
            </li>
          </ul>

          <div class="tab-content p-3 border border-top-0 rounded-bottom" id="resultTabsContent">
            <div class="tab-pane fade show active" id="summary" role="tabpanel" aria-labelledby="summary-tab">
              <div id="summary-content" class="content-panel"></div>
            </div>
            <div class="tab-pane fade" id="novelty" role="tabpanel" aria-labelledby="novelty-tab">
              <div id="novelty-content" class="content-panel"></div>
            </div>
            <div class="tab-pane fade" id="products" role="tabpanel" aria-labelledby="products-tab">
              <div id="products-content" class="content-panel"></div>
            </div>
            <div class="tab-pane fade" id="claim-chart" role="tabpanel" aria-labelledby="claim-chart-tab">
              <div id="claim-chart-content" class="content-panel"></div>
            </div>
          </div>

          <div class="mt-3 text-end">
            <button id="download-btn" class="btn btn-success">Download Report</button>
            <button id="debug-db-btn" class="btn btn-secondary ms-2">Debug DB</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="script.js"></script>
</body>
</html>