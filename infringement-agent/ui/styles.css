:root {
  --primary-color: #e60000;
  --secondary-color: #990000;
  --accent-color: #ff3333;
  --light-bg: #1a1a1a;
  --dark-bg: #000000;
  --text-color: #f8f9fa;
  --light-text: #f8f9fa;
  --border-color: #333333;
  --sidebar-width: 280px;
  --dark-text: #333333;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--light-bg);
  color: var(--text-color);
  min-height: 100vh;
}

.container-fluid {
  padding: 0;
  min-height: 100vh;
}

.row {
  margin: 0;
  min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
  background-color: var(--dark-bg);
  color: var(--light-text);
  min-height: 100vh;
  padding: 0;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.5);
  overflow-y: auto;
}

.sidebar-header {
  padding: 20px;
  background-color: var(--secondary-color);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.history-container {
  padding: 20px;
}

.history-container h5 {
  margin-bottom: 15px;
  color: var(--light-text);
  font-weight: 600;
}

#history-list .list-group-item {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--light-text);
  border: none;
  margin-bottom: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#history-list .list-group-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

#history-list .analyze-again {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

#history-list .analyze-again:hover {
  opacity: 1;
}

/* Main Content Styles */
.main-content {
  padding: 30px;
  min-height: 100vh;
  overflow-y: auto;
}

.content-header {
  margin-bottom: 30px;
  text-align: center;
}

.content-header h2 {
  color: var(--secondary-color);
  font-weight: 700;
  margin-bottom: 10px;
}

.input-container {
  max-width: 700px;
  margin: 0 auto 30px;
}

#patent-input {
  border-radius: 4px 0 0 4px;
  height: 50px;
  font-size: 16px;
}

#analyze-btn {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  border-radius: 0 4px 4px 0;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

#analyze-btn:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

#loading-indicator {
  margin: 50px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

#loading-indicator .spinner-border {
  width: 3rem;
  height: 3rem;
  margin-bottom: 15px;
}

#loading-indicator .progress {
  max-width: 500px;
  margin: 15px 0;
  height: 10px;
  width: 100%;
}

#loading-indicator p {
  margin-top: 15px;
}

/* Results Container Styles */
#results-container {
  margin-top: 30px;
  background-color: #222222;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.nav-tabs {
  border-bottom: none;
}

.nav-tabs .nav-link {
  color: var(--light-text);
  font-weight: 600;
  padding: 12px 20px;
  border: 1px solid transparent;
  border-radius: 8px 8px 0 0;
}

.nav-tabs .nav-link.active {
  color: var(--accent-color);
  background-color: #222222;
  border-color: var(--border-color);
  border-bottom-color: transparent;
}

.tab-content {
  background-color: #222222;
  min-height: 400px;
}

/* Content Styling */
.tab-pane {
  padding: 15px;
}

.content-panel {
  min-height: 300px;
}

.content-panel h1,
.content-panel h2,
.content-panel h3 {
  color: var(--primary-color);
  margin-top: 20px;
  margin-bottom: 15px;
  font-weight: 600;
}

.content-panel h1 {
  font-size: 1.8rem;
}

.content-panel h2 {
  font-size: 1.5rem;
}

.content-panel h3 {
  font-size: 1.3rem;
}

.content-panel p {
  margin-bottom: 15px;
  line-height: 1.6;
}

.content-panel ul,
.content-panel ol {
  margin-bottom: 15px;
  padding-left: 20px;
}

.content-panel li {
  margin-bottom: 5px;
}

.content-panel code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  color: #d63384;
}

.content-panel pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
  margin-bottom: 15px;
}

.content-panel table {
  width: 100%;
  margin-bottom: 20px;
  border-collapse: collapse;
  background-color: #2a2a2a;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.content-panel th,
.content-panel td {
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  text-align: left;
  vertical-align: top;
  word-wrap: break-word;
}

.content-panel th {
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.content-panel td {
  background-color: #2a2a2a;
  color: var(--light-text);
  line-height: 1.5;
}

.content-panel tr:nth-child(even) td {
  background-color: #333333;
}

.content-panel tr:hover td {
  background-color: #3a3a3a;
  transition: background-color 0.2s ease;
}

/* Specific styling for product tables */
.products-table th {
  background-color: var(--secondary-color);
}

.products-table .risk-high {
  color: #ff6b6b;
  font-weight: 600;
}

.products-table .risk-medium {
  color: #ffd93d;
  font-weight: 600;
}

.products-table .risk-low {
  color: #6bcf7f;
  font-weight: 600;
}

/* Specific styling for claim chart tables */
.claim-chart-table th {
  background-color: #1a472a;
}

.claim-chart-table td {
  font-size: 0.9rem;
}

.claim-chart-table .claim-element-col {
  width: 35%;
  font-weight: 500;
}

.claim-chart-table .feature-col {
  width: 35%;
}

.claim-chart-table .justification-col {
  width: 30%;
  font-style: italic;
}

/* Table responsiveness */
@media (max-width: 768px) {
  .content-panel table {
    font-size: 0.8rem;
  }

  .content-panel th,
  .content-panel td {
    padding: 8px 10px;
  }

  .claim-chart-table .claim-element-col,
  .claim-chart-table .feature-col,
  .claim-chart-table .justification-col {
    width: auto;
  }
}

/* Enhanced table styling for better readability */
.content-panel table.table-striped {
  border: 2px solid var(--primary-color);
}

.content-panel table.table-striped thead th {
  border-bottom: 2px solid rgba(255, 255, 255, 0.3);
}

.content-panel table.table-striped tbody tr:last-child td {
  border-bottom: none;
}

/* Loading state for tables */
.table-loading {
  opacity: 0.6;
  pointer-events: none;
}

.table-loading::after {
  content: "Loading...";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--dark-bg);
  padding: 10px 20px;
  border-radius: 4px;
  color: var(--light-text);
}

/* Evidence links styling */
.evidence-link {
  display: inline-block;
  margin: 5px 0;
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.2s ease;
}

.evidence-link:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

/* Product card styling */
.product-card {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.product-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-card h4 {
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: 10px;
}

/* Claim chart styling */
.claim-element {
  background-color: rgba(58, 110, 165, 0.1);
  border-left: 4px solid var(--primary-color);
  padding: 10px 15px;
  margin-bottom: 15px;
  border-radius: 0 4px 4px 0;
}

.product-evidence {
  background-color: rgba(255, 107, 107, 0.1);
  border-left: 4px solid var(--accent-color);
  padding: 10px 15px;
  margin-bottom: 15px;
  margin-left: 20px;
  border-radius: 0 4px 4px 0;
}

/* Download button styling */
.btn-success, .btn-primary {
  padding: 8px 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

.btn-success:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

/* Alert styling */
.alert {
  border-radius: 5px;
  margin-bottom: 20px;
}

/* Fix for d-none class */
.d-none {
  display: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    min-height: auto;
  }

  .main-content {
    padding: 20px 15px;
  }

  .content-header h2 {
    font-size: 1.5rem;
  }

  #patent-input, #analyze-btn {
    height: 45px;
  }

  .row {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    min-height: auto;
  }
}

/* Chat box styles */
.chat-container {
  background-color: #222222;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.chat-input {
  background-color: #333333;
  border: 1px solid var(--border-color);
  color: var(--light-text);
  border-radius: 4px 0 0 4px;
}

.chat-input:focus {
  background-color: #333333;
  color: var(--light-text);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(230, 0, 0, 0.25);
}

.chat-btn {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Confirmation dialog styles */
.confirmation-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1040;
}

.confirmation-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1050;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.confirmation-dialog .card {
  border: 2px solid #ffc107;
}

.confirmation-dialog .btn {
  min-width: 120px;
}

.chat-btn:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

/* Multi-patent styles */
.patent-list {
  background-color: #222222;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.patent-item {
  background-color: #333333;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.patent-item .status {
  font-size: 0.8rem;
  padding: 3px 8px;
  border-radius: 10px;
}

.status-pending {
  background-color: #666666;
}

.status-processing {
  background-color: var(--primary-color);
}

.status-completed {
  background-color: #006600;
}

.status-error {
  background-color: #cc0000;
}

.patent-family-checkbox {
  margin-top: 15px;
}

/* Force all placeholder text to be white/light */
::placeholder {
  color: var(--light-text) !important;
  opacity: 1 !important; /* override any faded look */
}

/* For WebKit browsers (Safari, Chrome) */
::-webkit-input-placeholder {
  color: var(--light-text) !important;
  opacity: 1 !important;
}

/* For Mozilla Firefox */
::-moz-placeholder {
  color: var(--light-text) !important;
  opacity: 1 !important;
}

:-ms-input-placeholder { /* Internet Explorer 10-11 */
  color: var(--light-text) !important;
  opacity: 1 !important;
}

::-ms-input-placeholder { /* Microsoft Edge */
  color: var(--light-text) !important;
  opacity: 1 !important;
}