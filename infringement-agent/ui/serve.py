#!/usr/bin/env python3
"""
Simple HTTP server for the Patent Infringement Analyzer UI.
This allows you to serve the UI files locally.
"""

import http.server
import socketserver
import os
import webbrowser
import base64
import json
import sys
from urllib.parse import urlparse

# Add the parent directory to the path to import database utilities
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
try:
    from tools.database_utils import get_complete_patent_analysis, init_db
except ImportError:
    print("Warning: Database utilities not available. Database features will be disabled.")
    def get_complete_patent_analysis(patent_number):
        return None
    def init_db():
        pass

# Configuration
PORT = 8080
DIRECTORY = os.path.dirname(os.path.abspath(__file__))


class Handler(http.server.SimpleHTTPRequestHandler):
    """Custom request handler with CORS support"""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)

    def do_GET(self):
        if self.path == '/download-report':
            # Create the full path to the generated_reports directory
            reports_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'generated_reports')

            # Find the latest report in the directory
            latest_report = self.get_latest_report(reports_dir)

            if latest_report:
                # Set headers for file download
                self.send_response(200)
                self.send_header('Content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                self.send_header('Content-Disposition', f'attachment; filename="{os.path.basename(latest_report)}"')
                self.end_headers()

                # Send the file content
                with open(latest_report, 'rb') as f:
                    self.wfile.write(f.read())
            else:
                # If no report is found, return a 404 error
                self.send_error(404, 'No report found')
        else:
            # For all other requests, use the default handler
            super().do_GET()

    def do_POST(self):
        print(f"POST request received for path: {self.path}")
        if self.path == '/api/check-patent':
            self.handle_check_patent()
        elif self.path == '/api/get-patent':
            self.handle_get_patent()
        else:
            print(f"Unknown API endpoint: {self.path}")
            self.send_error(404, 'API endpoint not found')

    def handle_check_patent(self):
        try:
            print("Handling check-patent request")
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            patent_number = data.get('patent_number')
            print(f"Checking patent: {patent_number}")

            if not patent_number:
                self.send_error(400, 'Patent number required')
                return

            # Check if patent exists in database
            try:
                patent_data = get_complete_patent_analysis(patent_number)
                print(f"Raw patent data: {patent_data}")
                exists = bool(patent_data and (
                    patent_data.get('analysis_data') or
                    patent_data.get('infringed_models') or
                    patent_data.get('novelty_data')
                ))
                print(f"Patent {patent_number} exists in database: {exists}")
                if patent_data:
                    print(f"Patent data keys: {list(patent_data.keys())}")
                    print(f"Analysis data type: {type(patent_data.get('analysis_data'))}")
                    print(f"Infringed models count: {len(patent_data.get('infringed_models', []))}")
            except Exception as db_error:
                print(f"Database error: {db_error}")
                exists = False

            response = {
                'exists': exists,
                'patent_number': patent_number
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))
            print(f"Response sent: {response}")

        except Exception as e:
            print(f"Error checking patent: {e}")
            self.send_error(500, f'Internal server error: {str(e)}')

    def handle_get_patent(self):
        try:
            print("Handling get-patent request")
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            patent_number = data.get('patent_number')
            print(f"Getting patent data for: {patent_number}")

            if not patent_number:
                self.send_error(400, 'Patent number required')
                return

            # Get complete patent data from database
            try:
                patent_data = get_complete_patent_analysis(patent_number)
                print(f"Patent data retrieved: {bool(patent_data)}")
            except Exception as db_error:
                print(f"Database error: {db_error}")
                patent_data = None

            if not patent_data:
                self.send_error(404, 'Patent not found in database')
                return

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(patent_data, default=str).encode('utf-8'))
            print("Patent data sent successfully")

        except Exception as e:
            print(f"Error getting patent: {e}")
            self.send_error(500, f'Internal server error: {str(e)}')

    def get_latest_report(self, directory):
        """Get the latest (most recently modified) report from a directory."""
        try:
            # Get all files in the directory
            files = [os.path.join(directory, f) for f in os.listdir(directory) if f.endswith('.xlsx')]
            
            # Return the most recently modified file
            if files:
                return max(files, key=os.path.getmtime)
        except FileNotFoundError:
            return None
        return None

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def main():
    """Start the server and open the browser"""
    print(f"Serving at http://localhost:{PORT}")

    # Initialize database
    try:
        init_db()
        print("Database initialized successfully")
    except Exception as e:
        print(f"Warning: Database initialization failed: {e}")

    # Create the server
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print("Server started. Press Ctrl+C to stop.")
        
        # Open the browser
        webbrowser.open(f"http://localhost:{PORT}")
        
        # Serve until interrupted
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()
