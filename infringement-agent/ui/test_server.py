#!/usr/bin/env python3
"""
Test script to verify the server and database functionality
"""

import requests
import json
import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def test_server():
    """Test the server endpoints"""
    base_url = "http://localhost:8080"
    
    print("Testing server endpoints...")
    
    # Test 1: Check if server is running
    try:
        response = requests.get(base_url)
        print(f"✅ Server is running (status: {response.status_code})")
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running. Please start the server first.")
        return False
    
    # Test 2: Test check-patent endpoint
    try:
        test_patent = "US7504937B2"
        response = requests.post(
            f"{base_url}/api/check-patent",
            headers={'Content-Type': 'application/json'},
            json={'patent_number': test_patent}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Check-patent endpoint working (patent {test_patent} exists: {data.get('exists', False)})")
        else:
            print(f"❌ Check-patent endpoint failed (status: {response.status_code})")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Check-patent endpoint error: {e}")
    
    # Test 3: Test get-patent endpoint (if patent exists)
    try:
        response = requests.post(
            f"{base_url}/api/get-patent",
            headers={'Content-Type': 'application/json'},
            json={'patent_number': test_patent}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Get-patent endpoint working (retrieved data for {test_patent})")
        elif response.status_code == 404:
            print(f"ℹ️  Get-patent endpoint working (patent {test_patent} not found in database)")
        else:
            print(f"❌ Get-patent endpoint failed (status: {response.status_code})")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Get-patent endpoint error: {e}")
    
    return True

def test_database():
    """Test database functionality directly"""
    print("\nTesting database functionality...")
    
    try:
        from tools.database_utils import init_db, get_complete_patent_analysis
        
        # Initialize database
        init_db()
        print("✅ Database initialized successfully")
        
        # Test getting patent data
        test_patent = "US7504937B2"
        patent_data = get_complete_patent_analysis(test_patent)
        
        if patent_data:
            print(f"✅ Patent {test_patent} found in database")
            print(f"   - Analysis data: {'Yes' if patent_data.get('analysis_data') else 'No'}")
            print(f"   - Infringed models: {len(patent_data.get('infringed_models', []))}")
        else:
            print(f"ℹ️  Patent {test_patent} not found in database (this is normal for first run)")
            
    except ImportError as e:
        print(f"❌ Database import error: {e}")
    except Exception as e:
        print(f"❌ Database error: {e}")

if __name__ == "__main__":
    print("🧪 Testing Patent Analysis Server\n")
    
    # Test database first
    test_database()
    
    # Test server endpoints
    if test_server():
        print("\n✅ All tests completed!")
    else:
        print("\n❌ Some tests failed!")
