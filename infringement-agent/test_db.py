#!/usr/bin/env python3
"""
Test script to verify database connectivity and data retrieval
"""

import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(__file__))

try:
    from tools.database_utils import get_complete_patent_analysis, init_db
    print("✅ Successfully imported database utilities")
except ImportError as e:
    print(f"❌ Failed to import database utilities: {e}")
    sys.exit(1)

def test_database():
    """Test database operations"""
    
    # Initialize database
    try:
        init_db()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False
    
    # Test with a known patent number
    test_patents = ['US9621967B2', 'US7504937B2', 'US10123456B2']
    
    for patent_number in test_patents:
        print(f"\n🔍 Testing patent: {patent_number}")
        
        try:
            patent_data = get_complete_patent_analysis(patent_number)
            
            if patent_data:
                print(f"✅ Patent {patent_number} found in database")
                print(f"   Data keys: {list(patent_data.keys())}")
                
                # Check specific fields
                if patent_data.get('analysis_data'):
                    print(f"   Analysis data: {type(patent_data['analysis_data'])} - {len(str(patent_data['analysis_data']))} chars")
                
                if patent_data.get('novelty_data'):
                    print(f"   Novelty data: {type(patent_data['novelty_data'])}")
                
                if patent_data.get('infringed_models'):
                    print(f"   Infringed models: {len(patent_data['infringed_models'])} items")
                    for i, model in enumerate(patent_data['infringed_models'][:2]):  # Show first 2
                        print(f"     Model {i+1}: {model.get('company', 'Unknown')} - {model.get('model', 'Unknown')}")
                
            else:
                print(f"❌ Patent {patent_number} not found in database")
                
        except Exception as e:
            print(f"❌ Error retrieving patent {patent_number}: {e}")
    
    return True

if __name__ == "__main__":
    print("🧪 Testing database connectivity and data retrieval...")
    test_database()
    print("\n✅ Database test completed")
