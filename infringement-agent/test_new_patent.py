#!/usr/bin/env python3
"""
Test script to verify that non-existent patents trigger new analysis correctly.
"""

import sys
import os

# Add the tools directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'tools'))

try:
    from tools.database_utils import get_complete_patent_analysis, init_db
    print("✅ Successfully imported database utilities")
except ImportError as e:
    print(f"❌ Failed to import database utilities: {e}")
    sys.exit(1)

def test_non_existent_patent():
    """Test that non-existent patents return proper has_data=False"""
    print("\n🔍 Testing non-existent patent handling...")
    
    # Test with a patent that doesn't exist
    test_patent = 'US11228184B2'
    
    print(f"\n📋 Testing patent: {test_patent}")
    
    try:
        patent_data = get_complete_patent_analysis(test_patent)
        
        if patent_data:
            print(f"✅ Patent data structure returned for {test_patent}")
            print(f"   - Has data: {patent_data.get('has_data', False)}")
            print(f"   - Analysis data: {patent_data.get('analysis_data', 'None')}")
            print(f"   - Analysis data length: {len(patent_data.get('analysis_data', ''))}")
            print(f"   - Novelty data available: {bool(patent_data.get('novelty_data'))}")
            print(f"   - Infringed models count: {len(patent_data.get('infringed_models', []))}")
            
            # Check if has_data is correctly set to False
            if patent_data.get('has_data', False) == False:
                print(f"   ✅ Correctly identified as having no meaningful data")
            else:
                print(f"   ❌ Incorrectly identified as having data")
                
            # Check if analysis_data is empty
            analysis_data = patent_data.get('analysis_data', '')
            if not analysis_data or analysis_data.strip() == '':
                print(f"   ✅ Analysis data is correctly empty")
            else:
                print(f"   ❌ Analysis data should be empty but contains: {analysis_data[:100]}...")
                
        else:
            print(f"❌ No data structure returned for {test_patent}")
            
    except Exception as e:
        print(f"❌ Error retrieving {test_patent}: {e}")

def test_existing_patent():
    """Test that existing patents return proper has_data=True"""
    print("\n🔍 Testing existing patent handling...")
    
    # Test with a patent that exists
    test_patent = 'US7504937B2'
    
    print(f"\n📋 Testing patent: {test_patent}")
    
    try:
        patent_data = get_complete_patent_analysis(test_patent)
        
        if patent_data:
            print(f"✅ Patent data structure returned for {test_patent}")
            print(f"   - Has data: {patent_data.get('has_data', False)}")
            print(f"   - Analysis data length: {len(patent_data.get('analysis_data', ''))}")
            print(f"   - Novelty data available: {bool(patent_data.get('novelty_data'))}")
            print(f"   - Infringed models count: {len(patent_data.get('infringed_models', []))}")
            
            # Check if has_data is correctly set to True
            if patent_data.get('has_data', False) == True:
                print(f"   ✅ Correctly identified as having meaningful data")
            else:
                print(f"   ❌ Incorrectly identified as having no data")
                
        else:
            print(f"❌ No data structure returned for {test_patent}")
            
    except Exception as e:
        print(f"❌ Error retrieving {test_patent}: {e}")

def main():
    """Run all tests"""
    print("🧪 Testing Non-Existent Patent Handling")
    print("=" * 50)
    
    # Initialize database
    try:
        init_db()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return
    
    # Test non-existent patent
    test_non_existent_patent()
    
    # Test existing patent for comparison
    test_existing_patent()
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")
    print("\n📝 Expected behavior:")
    print("   1. Non-existent patents should have has_data=False")
    print("   2. Non-existent patents should have empty analysis_data")
    print("   3. This should trigger new analysis in the UI")
    print("   4. Existing patents should have has_data=True")
    
    print("\n🚀 To test in UI:")
    print("   1. Run: cd infringement-agent/ui && python serve.py")
    print("   2. Open: http://localhost:8080")
    print("   3. Try analyzing: US11228184B2 (should start new analysis)")
    print("   4. Try analyzing: US7504937B2 (should show confirmation dialog)")

if __name__ == "__main__":
    main()
