"""Infringement Agent: provide infringement analysis for given patent number"""

import os
import time
import json

from dotenv import load_dotenv
from google.adk.agents import LlmAgent
from google.adk.tools.agent_tool import AgentTool

from . import prompt
from .sub_agents.claim_chart_generator import claim_chart_generator
from .sub_agents.collaboration_and_standards_search import collaboration_and_standards_search
from .sub_agents.competition_and_forward_citation_search import competition_and_forward_citation_search
from .sub_agents.novelty_analyzer import novelty_analyzer
from .sub_agents.product_search import product_search
from .tools.claim_chart_to_excel import claim_chart_tool
from .tools.get_patent_full_details_tool import get_patent_data
from .tools.get_standard import get_standard_tool
from .tools.litigation_competitor_search_tool import get_competitors_tool
from .tools.database_utils import (
    save_patent_tool, get_patent_tool, check_patent_tool,
    save_novelty_tool, get_novelty_tool, save_model_tool, 
    save_claim_chart_tool, get_complete_analysis_tool
)

load_dotenv()
MODEL = os.getenv("MODEL_ID")


infringement_agent = LlmAgent(
    name="infringement_agent",
    model=MODEL,
    description=(
        "You are the Best Patent Attorney and Infringement Analyst."
        "Your goal is to find products that may infringe on a given patent."
        "Use the tools available to you to find products that may infringe on a given patent"
        "Create claim charts for each product and create a professional report for the client."
    ),
    instruction=prompt.INFRINGEMENT_AGENT_PROMPT1,
    output_key="infringement_agent_output",
    tools=[
        get_patent_data,
        get_competitors_tool,
        get_standard_tool,
        AgentTool(agent=novelty_analyzer),
        AgentTool(agent=product_search),
        AgentTool(agent=competition_and_forward_citation_search),
        AgentTool(agent=collaboration_and_standards_search),
        AgentTool(agent=claim_chart_generator),
        claim_chart_tool,
        # Add the database tools
        save_patent_tool,
        get_patent_tool,
        check_patent_tool,
        save_novelty_tool,
        get_novelty_tool,
        save_model_tool,
        save_claim_chart_tool,
        get_complete_analysis_tool,
    ],
)

root_agent = infringement_agent
